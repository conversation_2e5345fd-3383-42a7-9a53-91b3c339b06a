import React from 'react'
import Link from 'next/link';
import { FaFacebookF, Fa<PERSON>witter, FaLinkedin, FaInstagram } from "react-icons/fa6";
import { cn } from '@/lib/utils';

const icons = [
    {
        id: 1,
        icon: <FaFacebookF />,
        name: 'facebookLink'
    },
    {
        id: 2,
        icon: <FaXTwitter />,
        name: 'twitterLink'
    },
    {
        id: 3,
        icon: <FaLinkedin />,
        name: 'linkedinLink'
    },
    {
        id: 4,
        icon: <FaInstagram />,
        name: 'InstagramLink'
    },

]
const SocalIcons = ({className, prentClass, links = {}}) => {
    return (
        <ul className={cn("flex items-center gap-[14px]", prentClass)}>
            {
                icons.map(({ icon, id, name }) => {
                    const href = links[name] || '#';
                    return (
                        <li key={id}>
                            <Link href={href} className={cn('rounded-md w-6 h-6 flex items-center justify-center border border-white border-opacity-20 text-cream-foreground hover:bg-primary transition-all duration-500', className)}>
                                {icon}
                            </Link>
                        </li>
                    )
                })
            }

        </ul>
    )
}

export default SocalIcons